package mk.finki.ukim.mk.library.config;

import mk.finki.ukim.mk.library.security.JwtHelper;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.SecurityFilterChain;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@TestConfiguration
@EnableWebSecurity
@Profile("test")
public class TestSecurityConfig {

    @Bean
    @Primary
    public SecurityFilterChain testSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth.anyRequest().permitAll())
                .sessionManagement(session -> session.sessionCreationPolicy(
                        org.springframework.security.config.http.SessionCreationPolicy.STATELESS
                ));
        return http.build();
    }

    @Bean
    @Primary
    @Profile("test")
    public JwtHelper testJwtHelper() {
        JwtHelper mockJwtHelper = Mockito.mock(JwtHelper.class);

        // Configure mock behavior
        when(mockJwtHelper.extractUsername(anyString())).thenReturn("testuser");
        when(mockJwtHelper.extractExpiration(anyString())).thenReturn(new Date(System.currentTimeMillis() + 86400000));
        when(mockJwtHelper.generateToken(any(UserDetails.class))).thenAnswer(invocation -> {
            UserDetails userDetails = invocation.getArgument(0);
            return "test-jwt-token-" + userDetails.getUsername();
        });
        when(mockJwtHelper.isValid(anyString(), any(UserDetails.class))).thenReturn(true);

        return mockJwtHelper;
    }
}