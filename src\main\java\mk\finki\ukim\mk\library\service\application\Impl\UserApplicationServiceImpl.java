package mk.finki.ukim.mk.library.service.application.Impl;

import mk.finki.ukim.mk.library.model.Dto.CreateUserDto;
import mk.finki.ukim.mk.library.model.Dto.DisplayUserDto;
import mk.finki.ukim.mk.library.model.Dto.LoginResponseDto;
import mk.finki.ukim.mk.library.model.Dto.LoginUserDto;
import mk.finki.ukim.mk.library.model.domain.User;
import mk.finki.ukim.mk.library.security.JwtHelper;
import mk.finki.ukim.mk.library.service.application.UserApplicationService;
import mk.finki.ukim.mk.library.service.domain.UserService;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class UserApplicationServiceImpl implements UserApplicationService {

    private final UserService userService;
    private final JwtHelper jwtHelper;

    public UserApplicationServiceImpl(UserService userService, JwtHelper jwtHelper) {
        this.userService = userService;
        this.jwtHelper = jwtHelper;
    }

    @Override
    public Optional<DisplayUserDto> register(CreateUserDto createUserDto) {
        try {
            User user = userService.register(
                    createUserDto.username(),
                    createUserDto.password(),
                    createUserDto.repeatPassword(),
                    createUserDto.name(),
                    createUserDto.surname(),
                    createUserDto.role()
            );
            return Optional.of(DisplayUserDto.from(user));
        } catch (Exception e) {
            // Let exceptions bubble up to controller for proper HTTP status handling
            throw e;
        }
    }

    @Override
    public Optional<LoginResponseDto> login(LoginUserDto loginUserDto) {
        try {
            User user = userService.login(
                    loginUserDto.username(),
                    loginUserDto.password()
            );

            String token = jwtHelper.generateToken(user);

            return Optional.of(new LoginResponseDto(token));
        } catch (Exception e) {
            // Let exceptions bubble up to controller for proper HTTP status handling
            throw e;
        }
    }


    @Override
    public Optional<DisplayUserDto> findByUsername(String username) {
        return Optional.of(DisplayUserDto.from(userService.findByUsername(username)));
    }
}
