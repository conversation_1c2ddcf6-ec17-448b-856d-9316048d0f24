package mk.finki.ukim.mk.library.security;

import mk.finki.ukim.mk.library.config.CustomUsernamePasswordAuthenticationProvider;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.nullValue;

/**
 * Integration tests for JWT Security Configuration
 * Tests security filter chain, CORS configuration, and endpoint access rules
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
class JwtSecurityWebConfigTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private JwtSecurityWebConfig securityConfig;

    @Test
    void corsConfigurationSource_ShouldAllowConfiguredOrigins() {
        // When
        CorsConfigurationSource corsSource = securityConfig.corsConfigurationSource();
        CorsConfiguration corsConfig = corsSource.getCorsConfiguration(null);

        // Then
        assertNotNull(corsConfig);
        assertTrue(corsConfig.getAllowedOrigins().contains("http://localhost:3001"));
        assertTrue(corsConfig.getAllowedMethods().contains("GET"));
        assertTrue(corsConfig.getAllowedMethods().contains("POST"));
        assertTrue(corsConfig.getAllowedMethods().contains("PUT"));
        assertTrue(corsConfig.getAllowedMethods().contains("DELETE"));
        assertTrue(corsConfig.getAllowedHeaders().contains("*"));
    }

    @Test
    void publicEndpoints_ShouldBeAccessibleWithoutAuthentication() throws Exception {
        // Test Swagger endpoints
        mockMvc.perform(get("/swagger-ui/index.html"))
                .andExpect(status().isOk());

        // Test API documentation
        mockMvc.perform(get("/v3/api-docs"))
                .andExpect(status().isOk());

        // Test public API endpoints
        mockMvc.perform(get("/api/countries"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/authors"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());
    }

    @Test
    void authenticationEndpoints_ShouldBeAccessibleWithoutAuthentication() throws Exception {
        // Test register endpoint accessibility (will fail due to missing data, but should not be forbidden)
        mockMvc.perform(post("/api/user/register")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isBadRequest()); // Bad request, not forbidden

        // Test login endpoint accessibility (will fail due to missing data, but should not be forbidden)
        mockMvc.perform(post("/api/user/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isBadRequest()); // Bad request, not forbidden
    }

    @Test
    void protectedEndpoints_ShouldRequireAuthentication() throws Exception {
        // Test wishlist endpoints require authentication
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(post("/api/wishlist/add-book/1"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(delete("/api/wishlist/remove-book/1"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void protectedEndpoints_ShouldBeAccessibleWithAuthentication() throws Exception {
        // Test wishlist endpoints with authentication
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/wishlist/books"))
                .andExpect(status().isOk());
    }

    @Test
    void corsHeaders_ShouldBePresent_WhenCorsEnabled() throws Exception {
        mockMvc.perform(options("/api/books")
                        .header("Origin", "http://localhost:3001")
                        .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "http://localhost:3001"))
                .andExpect(header().string("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE"));
    }

    @Test
    void csrfProtection_ShouldBeDisabled() throws Exception {
        // CSRF should be disabled, so POST requests without CSRF token should work
        mockMvc.perform(post("/api/user/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"username\":\"test\",\"password\":\"test\"}"))
                .andExpect(status().isNotFound()); // Not forbidden due to CSRF
    }

    @Test
    void sessionManagement_ShouldBeStateless() throws Exception {
        // Test that no session is created
        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk())
                .andExpect(request().sessionAttribute("SPRING_SECURITY_CONTEXT", nullValue()));
    }

    @Test
    void jwtFilter_ShouldBeConfigured() {
        // Verify that JwtFilter is properly configured in the security chain
        assertNotNull(securityConfig);
        // The filter chain configuration is tested through integration tests
    }

    @Test
    void authenticationProvider_ShouldBeConfigured() {
        // Verify that CustomUsernamePasswordAuthenticationProvider is available
        assertNotNull(securityConfig);
        // The authentication provider is tested through its own unit tests
    }

    @Test
    void unauthorizedEndpoint_ShouldReturn401() throws Exception {
        // Test accessing a protected endpoint without authentication
        mockMvc.perform(post("/api/wishlist/add-book/1"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void invalidJwtToken_ShouldReturn401() throws Exception {
        // Test with invalid JWT token
        mockMvc.perform(get("/api/wishlist")
                        .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void malformedAuthorizationHeader_ShouldReturn401() throws Exception {
        // Test with malformed authorization header
        mockMvc.perform(get("/api/wishlist")
                        .header("Authorization", "InvalidFormat token"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void emptyAuthorizationHeader_ShouldReturn401() throws Exception {
        // Test with empty authorization header
        mockMvc.perform(get("/api/wishlist")
                        .header("Authorization", ""))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "librarian", roles = "LIBRARIAN")
    void librarianUser_ShouldAccessProtectedEndpoints() throws Exception {
        // Test that librarian can access protected endpoints
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());
    }

    @Test
    void httpBasicAuthentication_ShouldBeDisabled() throws Exception {
        // Test that HTTP Basic authentication is disabled
        mockMvc.perform(get("/api/wishlist")
                        .header("Authorization", "Basic dGVzdDp0ZXN0")) // base64 encoded "test:test"
                .andExpect(status().isUnauthorized());
    }

    @Test
    void formLogin_ShouldBeDisabled() throws Exception {
        // Test that form login is disabled
        mockMvc.perform(post("/login")
                        .param("username", "test")
                        .param("password", "test"))
                .andExpect(status().isNotFound()); // Should not find login form endpoint
    }
}
