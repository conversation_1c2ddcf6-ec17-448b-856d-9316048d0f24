package mk.finki.ukim.mk.library.security;

import mk.finki.ukim.mk.library.config.TestSecurityConfig;
import mk.finki.ukim.mk.library.model.domain.User;
import mk.finki.ukim.mk.library.model.enumerations.Role;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Authorization functionality
 * Tests role-based access control and method-level security
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Import(TestSecurityConfig.class)
class AuthorizationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void publicEndpoints_ShouldBeAccessible_WithoutAuthentication() throws Exception {
        // Test public book endpoints
        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/authors"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/countries"))
                .andExpect(status().isOk());
    }

    @Test
    void authenticationEndpoints_ShouldBeAccessible_WithoutAuthentication() throws Exception {
        // Test authentication endpoints are public
        mockMvc.perform(post("/api/user/register"))
                .andExpect(status().isBadRequest()); // Bad request due to missing data, not unauthorized

        mockMvc.perform(post("/api/user/login"))
                .andExpect(status().isBadRequest()); // Bad request due to missing data, not unauthorized
    }

    @Test
    void protectedEndpoints_ShouldRequireAuthentication() throws Exception {
        // Test wishlist endpoints require authentication
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(get("/api/wishlist/books"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(post("/api/wishlist/add-book/1"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(delete("/api/wishlist/remove-book/1"))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(post("/api/wishlist/borrow-all"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "user", roles = "USER")
    void userRole_ShouldAccessWishlistEndpoints() throws Exception {
        // Test that regular users can access wishlist endpoints
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/wishlist/books"))
                .andExpect(status().isOk());

        // Note: POST/DELETE operations might fail due to business logic, but should not be forbidden
        mockMvc.perform(post("/api/wishlist/add-book/1"))
                .andExpect(status().isNotFound()); // Book not found, not forbidden

        mockMvc.perform(delete("/api/wishlist/remove-book/1"))
                .andExpect(status().isNotFound()); // Book not found, not forbidden
    }

    @Test
    @WithMockUser(username = "librarian", roles = "LIBRARIAN")
    void librarianRole_ShouldAccessWishlistEndpoints() throws Exception {
        // Test that librarians can access wishlist endpoints
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/wishlist/books"))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/wishlist/add-book/1"))
                .andExpect(status().isNotFound()); // Book not found, not forbidden

        mockMvc.perform(delete("/api/wishlist/remove-book/1"))
                .andExpect(status().isNotFound()); // Book not found, not forbidden
    }

    @Test
    @WithMockUser(username = "user", roles = "USER")
    void userRole_ShouldAccessPublicEndpoints() throws Exception {
        // Test that authenticated users can still access public endpoints
        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/authors"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/countries"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "librarian", roles = "LIBRARIAN")
    void librarianRole_ShouldAccessPublicEndpoints() throws Exception {
        // Test that librarians can access public endpoints
        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/authors"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/countries"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "user", roles = "USER")
    void preAuthorizeAnnotation_ShouldEnforceAuthentication_OnWishlistEndpoints() throws Exception {
        // Test that @PreAuthorize("isAuthenticated()") works on wishlist endpoints
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk()); // Should be OK because user is authenticated

        mockMvc.perform(get("/api/wishlist/books"))
                .andExpect(status().isOk()); // Should be OK because user is authenticated
    }

    @Test
    void swaggerEndpoints_ShouldBeAccessible_WithoutAuthentication() throws Exception {
        // Test Swagger UI endpoints are public
        mockMvc.perform(get("/swagger-ui/index.html"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/v3/api-docs"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "user", roles = "USER")
    void authenticatedUser_ShouldAccessSwaggerEndpoints() throws Exception {
        // Test that authenticated users can access Swagger
        mockMvc.perform(get("/swagger-ui/index.html"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/v3/api-docs"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "librarian", roles = "LIBRARIAN")
    void librarianRole_ShouldAccessBookManagementEndpoints() throws Exception {
        // Test that librarians can access book management endpoints
        // Note: These endpoints are currently commented out with @PreAuthorize in the actual code
        // but they should be accessible to librarians when uncommented
        
        mockMvc.perform(post("/api/books/add"))
                .andExpect(status().isBadRequest()); // Bad request due to missing data, not forbidden

        mockMvc.perform(put("/api/books/1/mark-as-borrowed"))
                .andExpect(status().isNotFound()); // Book not found, not forbidden
    }

    @Test
    @WithMockUser(username = "user", roles = "USER")
    void userRole_ShouldAccessBookManagementEndpoints_WhenNotRestricted() throws Exception {
        // Test that regular users can access book management endpoints
        // (since @PreAuthorize is commented out in the actual code)
        
        mockMvc.perform(post("/api/books/add"))
                .andExpect(status().isBadRequest()); // Bad request due to missing data, not forbidden

        mockMvc.perform(put("/api/books/1/mark-as-borrowed"))
                .andExpect(status().isNotFound()); // Book not found, not forbidden
    }

    @Test
    void roleHierarchy_ShouldNotExist_InCurrentConfiguration() {
        // Test that there's no role hierarchy (LIBRARIAN doesn't inherit USER permissions)
        // This is verified through the individual role tests above
        // Both roles have the same access level in the current configuration
    }

    @Test
    @WithMockUser(username = "admin", roles = "ADMIN")
    void unknownRole_ShouldStillAccessPublicEndpoints() throws Exception {
        // Test that unknown roles can still access public endpoints
        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/authors"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "admin", roles = "ADMIN")
    void unknownRole_ShouldAccessProtectedEndpoints_WhenAuthenticated() throws Exception {
        // Test that unknown roles can access protected endpoints if authenticated
        // (since the current configuration only checks for authentication, not specific roles)
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "user", roles = {})
    void userWithoutRoles_ShouldStillAccessEndpoints_WhenAuthenticated() throws Exception {
        // Test that users without roles can still access endpoints if authenticated
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "user", roles = {"USER", "LIBRARIAN"})
    void userWithMultipleRoles_ShouldAccessAllEndpoints() throws Exception {
        // Test that users with multiple roles can access all endpoints
        mockMvc.perform(get("/api/wishlist"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk());

        mockMvc.perform(post("/api/books/add"))
                .andExpect(status().isBadRequest()); // Bad request due to missing data, not forbidden
    }
}
