# Test profile configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Disable components that cause issues in tests
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

# Disable scheduling
spring.task.scheduling.enabled=false

# Minimal logging
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN
logging.level.com.zaxxer.hikari=WARN
logging.level.root=WARN

# Disable unnecessary features for tests
spring.main.banner-mode=off
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false